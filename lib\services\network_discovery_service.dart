import 'dart:async';
import 'dart:io';
import 'package:multicast_dns/multicast_dns.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:flutter/foundation.dart';

/// Service for discovering devices on the local network
/// Handles Windows-specific networking limitations
class NetworkDiscoveryService {
  static const String _serviceType = '_vaultwarden-desktop._tcp';

  MDnsClient? _mdnsClient;
  Timer? _discoveryTimer;
  final StreamController<Map<String, String>> _discoveredDevicesController = StreamController.broadcast();
  final bool _isWindowsPlatform = Platform.isWindows;
  bool _mdnsSupported = true;

  /// Stream of discovered devices (IP address -> device info)
  Stream<Map<String, String>> get discoveredDevices => _discoveredDevicesController.stream;

  /// Check if mDNS is supported on current platform
  bool get isMdnsSupported => _mdnsSupported && !_isWindowsPlatform;

  /// Start mDNS service discovery with Windows compatibility
  Future<void> startDiscovery() async {
    if (_isWindowsPlatform) {
      debugPrint('mDNS discovery limited on Windows platform - using alternative discovery methods');
      _mdnsSupported = false;
      await _startWindowsCompatibleDiscovery();
      return;
    }

    try {
      _mdnsClient = MDnsClient();
      await _mdnsClient!.start();

      // Start periodic discovery
      _discoveryTimer = Timer.periodic(const Duration(seconds: 10), (_) {
        _performDiscovery();
      });

      // Perform initial discovery
      _performDiscovery();
      debugPrint('mDNS discovery started successfully');
    } catch (e) {
      debugPrint('Error starting mDNS discovery: $e');
      _mdnsSupported = false;
      // Fallback to Windows-compatible discovery
      await _startWindowsCompatibleDiscovery();
    }
  }

  /// Stop discovery service
  Future<void> stopDiscovery() async {
    _discoveryTimer?.cancel();
    _discoveryTimer = null;

    try {
      _mdnsClient?.stop();
      _mdnsClient = null;
    } catch (e) {
      debugPrint('Error stopping mDNS discovery: $e');
    }
  }

  /// Windows-compatible discovery method using network scanning
  Future<void> _startWindowsCompatibleDiscovery() async {
    debugPrint('Starting Windows-compatible network discovery');

    // Start periodic network scanning instead of mDNS
    _discoveryTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _performNetworkScan();
    });

    // Perform initial scan
    _performNetworkScan();
  }

  /// Perform network scan for Windows compatibility
  Future<void> _performNetworkScan() async {
    try {
      final ipAddress = await getCurrentIPAddress();
      if (ipAddress == null) return;

      debugPrint('Scanning network from IP: $ipAddress');

      // Extract network prefix (e.g., 192.168.1.x)
      final parts = ipAddress.split('.');
      if (parts.length != 4) return;

      final networkPrefix = '${parts[0]}.${parts[1]}.${parts[2]}';

      // Scan common SSH ports on local network
      for (int i = 1; i <= 254; i++) {
        final targetIp = '$networkPrefix.$i';
        if (targetIp == ipAddress) continue; // Skip self

        // Check if host responds on SSH port (non-blocking)
        _checkHostForVaultwardenService(targetIp);
      }
    } catch (e) {
      debugPrint('Error during network scan: $e');
    }
  }

  /// Check if a host is running Vaultwarden service
  Future<void> _checkHostForVaultwardenService(String ipAddress) async {
    try {
      // Quick connection test to SSH port
      final reachable = await canReachHost(ipAddress, port: 22, timeoutSeconds: 1);
      if (reachable) {
        // If SSH is available, assume it might be a Vaultwarden device
        final deviceInfo = {
          'name': 'Device-$ipAddress',
          'ip': ipAddress,
          'port': '22',
          'target': ipAddress,
          'discovery_method': 'network_scan',
        };

        _discoveredDevicesController.add({ipAddress: deviceInfo.toString()});
        debugPrint('Found potential Vaultwarden device at $ipAddress');
      }
    } catch (e) {
      // Silently ignore connection failures during scanning
    }
  }

  /// Perform a single discovery scan
  Future<void> _performDiscovery() async {
    if (_mdnsClient == null) return;

    try {
      // Look for Vaultwarden desktop services
      await for (final PtrResourceRecord ptr in _mdnsClient!
          .lookup<PtrResourceRecord>(ResourceRecordQuery.serverPointer(_serviceType))) {
        
        // Get service details
        await for (final SrvResourceRecord srv in _mdnsClient!
            .lookup<SrvResourceRecord>(ResourceRecordQuery.service(ptr.domainName))) {
          
          // Get additional info (TXT records)
          final txtRecords = <String, String>{};
          await for (final TxtResourceRecord txt in _mdnsClient!
              .lookup<TxtResourceRecord>(ResourceRecordQuery.text(ptr.domainName))) {
            
            // TXT records are stored as String, split by key=value format
            final recordString = txt.text;
            final parts = recordString.split('=');
            if (parts.length == 2) {
              txtRecords[parts[0]] = parts[1];
            }
          }

          // Get IP address
          await for (final IPAddressResourceRecord ip in _mdnsClient!
              .lookup<IPAddressResourceRecord>(ResourceRecordQuery.addressIPv4(srv.target))) {
            
            final deviceInfo = {
              'name': ptr.domainName,
              'ip': ip.address.address,
              'port': srv.port.toString(),
              'target': srv.target,
              ...txtRecords,
            };
            
            _discoveredDevicesController.add({ip.address.address: deviceInfo.toString()});
          }
        }
      }
    } catch (e) {
      debugPrint('Error during discovery: $e');
    }
  }

  /// Advertise this desktop as a Vaultwarden service
  Future<void> advertiseService({
    required int sshPort,
    required String publicKeyFingerprint,
    String? deviceName,
  }) async {
    if (_mdnsClient == null && !_isWindowsPlatform) {
      await startDiscovery();
    }

    try {
      final hostName = deviceName ?? await _getHostName();

      // Get local IP address
      final wifiIP = await NetworkInfo().getWifiIP();
      if (wifiIP == null) return;

      // Create service advertisement info
      debugPrint('Advertising Vaultwarden desktop service on $wifiIP:$sshPort');
      debugPrint('Host: $hostName, Fingerprint: $publicKeyFingerprint');

      if (_isWindowsPlatform) {
        debugPrint('Windows platform: mDNS service registration not available, using alternative methods');
        // On Windows, we could use other discovery mechanisms like UPnP or custom broadcast
      } else {
        // Note: Actual mDNS service registration would require platform-specific implementation
        // For now, we just log the advertisement details
      }
    } catch (e) {
      debugPrint('Error advertising service: $e');
    }
  }

  /// Get the local hostname
  Future<String> _getHostName() async {
    try {
      final result = await Process.run('hostname', []);
      return result.stdout.toString().trim();
    } catch (e) {
      return Platform.localHostname;
    }
  }

  /// Get current Wi-Fi IP address
  Future<String?> getCurrentIPAddress() async {
    try {
      return await NetworkInfo().getWifiIP();
    } catch (e) {
      debugPrint('Error getting IP address: $e');
      return null;
    }
  }

  /// Check if we can reach a specific IP address
  Future<bool> canReachHost(String ipAddress, {int port = 22, int timeoutSeconds = 5}) async {
    try {
      final socket = await Socket.connect(
        ipAddress,
        port,
        timeout: Duration(seconds: timeoutSeconds),
      );
      await socket.close();
      return true;
    } catch (e) {
      return false;
    }
  }

  void dispose() {
    stopDiscovery();
    _discoveredDevicesController.close();
  }
}
