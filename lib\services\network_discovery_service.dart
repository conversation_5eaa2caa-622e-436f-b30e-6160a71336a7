import 'dart:async';
import 'dart:io';
import 'package:multicast_dns/multicast_dns.dart';
import 'package:network_info_plus/network_info_plus.dart';

/// Service for discovering devices on the local network
class NetworkDiscoveryService {
  static const String _serviceType = '_vaultwarden-desktop._tcp';
  
  MDnsClient? _mdnsClient;
  Timer? _discoveryTimer;
  final StreamController<Map<String, String>> _discoveredDevicesController = StreamController.broadcast();

  /// Stream of discovered devices (IP address -> device info)
  Stream<Map<String, String>> get discoveredDevices => _discoveredDevicesController.stream;

  /// Start mDNS service discovery
  Future<void> startDiscovery() async {
    try {
      _mdnsClient = MDnsClient();
      await _mdnsClient!.start();

      // Start periodic discovery
      _discoveryTimer = Timer.periodic(const Duration(seconds: 10), (_) {
        _performDiscovery();
      });

      // Perform initial discovery
      _performDiscovery();
    } catch (e) {
      print('Error starting mDNS discovery: $e');
    }
  }

  /// Stop discovery service
  Future<void> stopDiscovery() async {
    _discoveryTimer?.cancel();
    _discoveryTimer = null;
    
    try {
      _mdnsClient?.stop();
      _mdnsClient = null;
    } catch (e) {
      print('Error stopping mDNS discovery: $e');
    }
  }

  /// Perform a single discovery scan
  Future<void> _performDiscovery() async {
    if (_mdnsClient == null) return;

    try {
      // Look for Vaultwarden desktop services
      await for (final PtrResourceRecord ptr in _mdnsClient!
          .lookup<PtrResourceRecord>(ResourceRecordQuery.serverPointer(_serviceType))) {
        
        // Get service details
        await for (final SrvResourceRecord srv in _mdnsClient!
            .lookup<SrvResourceRecord>(ResourceRecordQuery.service(ptr.domainName))) {
          
          // Get additional info (TXT records)
          final txtRecords = <String, String>{};
          await for (final TxtResourceRecord txt in _mdnsClient!
              .lookup<TxtResourceRecord>(ResourceRecordQuery.text(ptr.domainName))) {
            
            // TXT records are stored as String, split by key=value format
            final recordString = txt.text;
            final parts = recordString.split('=');
            if (parts.length == 2) {
              txtRecords[parts[0]] = parts[1];
            }
          }

          // Get IP address
          await for (final IPAddressResourceRecord ip in _mdnsClient!
              .lookup<IPAddressResourceRecord>(ResourceRecordQuery.addressIPv4(srv.target))) {
            
            final deviceInfo = {
              'name': ptr.domainName,
              'ip': ip.address.address,
              'port': srv.port.toString(),
              'target': srv.target,
              ...txtRecords,
            };
            
            _discoveredDevicesController.add({ip.address.address: deviceInfo.toString()});
          }
        }
      }
    } catch (e) {
      print('Error during discovery: $e');
    }
  }

  /// Advertise this desktop as a Vaultwarden service
  Future<void> advertiseService({
    required int sshPort,
    required String publicKeyFingerprint,
    String? deviceName,
  }) async {
    if (_mdnsClient == null) {
      await startDiscovery();
    }

    try {
      final hostName = deviceName ?? await _getHostName();
      
      // Get local IP address
      final wifiIP = await NetworkInfo().getWifiIP();
      if (wifiIP == null) return;

      // Create service advertisement info
      print('Advertising Vaultwarden desktop service on $wifiIP:$sshPort');
      print('Host: $hostName, Fingerprint: $publicKeyFingerprint');
      
      // Note: Actual mDNS service registration would require platform-specific implementation
      // For now, we just log the advertisement details
    } catch (e) {
      print('Error advertising service: $e');
    }
  }

  /// Get the local hostname
  Future<String> _getHostName() async {
    try {
      final result = await Process.run('hostname', []);
      return result.stdout.toString().trim();
    } catch (e) {
      return Platform.localHostname;
    }
  }

  /// Get current Wi-Fi IP address
  Future<String?> getCurrentIPAddress() async {
    try {
      return await NetworkInfo().getWifiIP();
    } catch (e) {
      print('Error getting IP address: $e');
      return null;
    }
  }

  /// Check if we can reach a specific IP address
  Future<bool> canReachHost(String ipAddress, {int port = 22, int timeoutSeconds = 5}) async {
    try {
      final socket = await Socket.connect(
        ipAddress,
        port,
        timeout: Duration(seconds: timeoutSeconds),
      );
      await socket.close();
      return true;
    } catch (e) {
      return false;
    }
  }

  void dispose() {
    stopDiscovery();
    _discoveredDevicesController.close();
  }
}
