import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:process_run/shell.dart';
import 'package:vaullt/services/docker_service.dart';
import 'package:vaullt/utils/error_handler.dart';

import 'docker_service_test.mocks.dart';

@GenerateMocks([Shell])
void main() {
  group('DockerService', () {
    late DockerService dockerService;
    late MockShell mockShell;
    const testComposePath = '/test/docker-compose.yaml';

    setUp(() {
      mockShell = MockShell();
      dockerService = DockerService(dockerComposePath: testComposePath);
      // Note: In a real implementation, you'd need to inject the mock shell
    });

    group('isContainerRunning', () {
      test('returns true when container is running', () async {
        // Arrange
        when(mockShell.run('docker-compose -f "$testComposePath" ps -q vaultwarden'))
            .thenAnswer((_) async => [
              ProcessResult(
                0,
                0,
                'container123',
                '',
              )
            ]);

        when(mockShell.run('docker inspect --format="{{.State.Running}}" container123'))
            .thenAnswer((_) async => [
              ProcessResult(
                0,
                0,
                'true',
                '',
              )
            ]);

        // Act
        final result = await dockerService.isContainerRunning();

        // Assert
        expect(result, isTrue);
      });

      test('returns false when container is not running', () async {
        // Arrange
        when(mockShell.run('docker-compose -f "$testComposePath" ps -q vaultwarden'))
            .thenAnswer((_) async => [
              ProcessResult(
                0,
                0,
                'container123',
                '',
              )
            ]);

        when(mockShell.run('docker inspect --format="{{.State.Running}}" container123'))
            .thenAnswer((_) async => [
              ProcessResult(
                0,
                0,
                'false',
                '',
              )
            ]);

        // Act
        final result = await dockerService.isContainerRunning();

        // Assert
        expect(result, isFalse);
      });

      test('returns false when no container found', () async {
        // Arrange
        when(mockShell.run('docker-compose -f "$testComposePath" ps -q vaultwarden'))
            .thenAnswer((_) async => [
              ProcessResult(
                0,
                0,
                '',
                '',
              )
            ]);

        // Act
        final result = await dockerService.isContainerRunning();

        // Assert
        expect(result, isFalse);
      });

      test('returns false when shell command throws exception', () async {
        // Arrange
        when(mockShell.run('docker-compose -f "$testComposePath" ps -q vaultwarden'))
            .thenThrow(Exception('Docker not available'));

        // Act
        final result = await dockerService.isContainerRunning();

        // Assert
        expect(result, isFalse);
      });
    });

    group('startContainer', () {
      test('executes start command successfully', () async {
        // Arrange
        when(mockShell.run('docker-compose -f "$testComposePath" up -d'))
            .thenAnswer((_) async => [
              ProcessResult(
                0,
                0,
                'Container started',
                '',
              )
            ]);

        // Act & Assert
        expect(() => dockerService.startContainer(), returnsNormally);
      });

      test('throws DockerException when start command fails', () async {
        // Arrange
        when(mockShell.run('docker-compose -f "$testComposePath" up -d'))
            .thenThrow(Exception('Docker daemon not running'));

        // Act & Assert
        expect(
          () => dockerService.startContainer(),
          throwsA(isA<DockerException>()),
        );
      });
    });

    group('stopContainer', () {
      test('executes stop command successfully', () async {
        // Arrange
        when(mockShell.run('docker-compose -f "$testComposePath" down'))
            .thenAnswer((_) async => [
              ProcessResult(
                0,
                0,
                'Container stopped',
                '',
              )
            ]);

        // Act & Assert
        expect(() => dockerService.stopContainer(), returnsNormally);
      });

      test('throws DockerException when stop command fails', () async {
        // Arrange
        when(mockShell.run('docker-compose -f "$testComposePath" down'))
            .thenThrow(Exception('Container not found'));

        // Act & Assert
        expect(
          () => dockerService.stopContainer(),
          throwsA(isA<DockerException>()),
        );
      });
    });

    group('isDockerAvailable', () {
      test('returns true when Docker and Docker Compose are available', () async {
        // Arrange
        when(mockShell.run('docker --version'))
            .thenAnswer((_) async => [
              ProcessResult(
                0,
                0,
                'Docker version 20.10.0',
                '',
              )
            ]);

        when(mockShell.run('docker-compose --version'))
            .thenAnswer((_) async => [
              ProcessResult(
                0,
                0,
                'docker-compose version 1.29.0',
                '',
              )
            ]);

        // Act
        final result = await dockerService.isDockerAvailable();

        // Assert
        expect(result, isTrue);
      });

      test('returns false when Docker is not available', () async {
        // Arrange
        when(mockShell.run('docker --version'))
            .thenThrow(Exception('Command not found'));

        // Act
        final result = await dockerService.isDockerAvailable();

        // Assert
        expect(result, isFalse);
      });

      test('returns false when Docker Compose is not available', () async {
        // Arrange
        when(mockShell.run('docker --version'))
            .thenAnswer((_) async => [
              ProcessResult(
                0,
                0,
                'Docker version 20.10.0',
                '',
              )
            ]);

        when(mockShell.run('docker-compose --version'))
            .thenThrow(Exception('Command not found'));

        // Act
        final result = await dockerService.isDockerAvailable();

        // Assert
        expect(result, isFalse);
      });
    });

    group('getLogs', () {
      test('returns container logs successfully', () async {
        // Arrange
        const expectedLogs = 'Container log line 1\nContainer log line 2';
        when(mockShell.run('docker-compose -f "$testComposePath" logs --tail 100 vaultwarden'))
            .thenAnswer((_) async => [
              ProcessResult(
                0,
                0,
                expectedLogs,
                '',
              )
            ]);

        // Act
        final result = await dockerService.getLogs();

        // Assert
        expect(result, equals(expectedLogs));
      });

      test('returns error message when logs command fails', () async {
        // Arrange
        when(mockShell.run('docker-compose -f "$testComposePath" logs --tail 100 vaultwarden'))
            .thenThrow(Exception('Container not found'));

        // Act
        final result = await dockerService.getLogs();

        // Assert
        expect(result, contains('Error retrieving logs'));
      });

      test('respects custom line count parameter', () async {
        // Arrange
        const customLines = 50;
        when(mockShell.run('docker-compose -f "$testComposePath" logs --tail $customLines vaultwarden'))
            .thenAnswer((_) async => [
              ProcessResult(
                0,
                0,
                'Custom logs',
                '',
              )
            ]);

        // Act
        final result = await dockerService.getLogs(lines: customLines);

        // Assert
        expect(result, equals('Custom logs'));
        verify(mockShell.run('docker-compose -f "$testComposePath" logs --tail $customLines vaultwarden'));
      });
    });
  });
}

// Mock ProcessResult class for testing
class ProcessResult {
  final int pid;
  final int exitCode;
  final dynamic stdout;
  final dynamic stderr;

  ProcessResult(this.pid, this.exitCode, this.stdout, this.stderr);
}
