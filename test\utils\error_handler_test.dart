import 'package:flutter_test/flutter_test.dart';
import 'package:vaullt/utils/error_handler.dart';

void main() {
  group('ErrorHandler', () {
    group('handleError', () {
      test('returns VaAulLTException unchanged', () {
        const originalException = NetworkException('Test network error');
        
        final result = ErrorHandler.handleError(originalException);
        
        expect(result, equals(originalException));
      });

      test('categorizes network-related errors', () {
        final networkErrors = [
          'socket connection failed',
          'network timeout',
          'dns resolution failed',
          'connection refused',
          'host unreachable',
        ];

        for (final errorMsg in networkErrors) {
          final result = ErrorHandler.handleError(Exception(errorMsg));
          expect(result, isA<NetworkException>());
          expect(result.message, contains(errorMsg));
        }
      });

      test('categorizes docker-related errors', () {
        final dockerErrors = [
          'docker daemon not running',
          'container not found',
          'docker-compose failed',
        ];

        for (final errorMsg in dockerErrors) {
          final result = ErrorHandler.handleError(Exception(errorMsg));
          expect(result, isA<DockerException>());
          expect(result.message, contains(errorMsg));
        }
      });

      test('categorizes ssh-related errors', () {
        final sshErrors = [
          'ssh connection failed',
          'authentication failed',
          'key not found',
          'tunnel error',
        ];

        for (final errorMsg in sshErrors) {
          final result = ErrorHandler.handleError(Exception(errorMsg));
          expect(result, isA<SSHException>());
          expect(result.message, contains(errorMsg));
        }
      });

      test('categorizes configuration-related errors', () {
        final configErrors = [
          'config file not found',
          'yaml parse error',
          'invalid configuration',
        ];

        for (final errorMsg in configErrors) {
          final result = ErrorHandler.handleError(Exception(errorMsg));
          expect(result, isA<ConfigurationException>());
          expect(result.message, contains(errorMsg));
        }
      });

      test('defaults to ServiceException for unknown errors', () {
        final result = ErrorHandler.handleError(Exception('unknown error'));
        
        expect(result, isA<ServiceException>());
        expect(result.message, contains('unknown error'));
      });

      test('includes context in error message', () {
        const context = 'test operation';
        final result = ErrorHandler.handleError(
          Exception('test error'),
          context: context,
        );
        
        expect(result.message, contains(context));
      });

      test('preserves original error and stack trace', () {
        final originalError = Exception('original error');
        final stackTrace = StackTrace.current;
        
        final result = ErrorHandler.handleError(
          originalError,
          stackTrace: stackTrace,
        );
        
        expect(result.originalError, equals(originalError));
        expect(result.stackTrace, equals(stackTrace));
      });
    });

    group('getUserFriendlyMessage', () {
      test('returns appropriate message for NetworkException', () {
        const exception = NetworkException('Network error');
        final message = ErrorHandler.getUserFriendlyMessage(exception);
        
        expect(message, contains('Network connection issue'));
      });

      test('returns appropriate message for DockerException', () {
        const exception = DockerException('Docker error');
        final message = ErrorHandler.getUserFriendlyMessage(exception);
        
        expect(message, contains('Docker service issue'));
      });

      test('returns appropriate message for SSHException', () {
        const exception = SSHException('SSH error');
        final message = ErrorHandler.getUserFriendlyMessage(exception);
        
        expect(message, contains('SSH connection issue'));
      });

      test('returns appropriate message for ConfigurationException', () {
        const exception = ConfigurationException('Config error');
        final message = ErrorHandler.getUserFriendlyMessage(exception);
        
        expect(message, contains('Configuration error'));
      });

      test('returns generic message for unknown errors', () {
        final message = ErrorHandler.getUserFriendlyMessage(Exception('unknown'));
        
        expect(message, contains('unexpected error occurred'));
      });
    });

    group('isRecoverable', () {
      test('returns true for NetworkException', () {
        const exception = NetworkException('Network error');
        expect(ErrorHandler.isRecoverable(exception), isTrue);
      });

      test('returns true for SSHException', () {
        const exception = SSHException('SSH error');
        expect(ErrorHandler.isRecoverable(exception), isTrue);
      });

      test('returns false for DockerException', () {
        const exception = DockerException('Docker error');
        expect(ErrorHandler.isRecoverable(exception), isFalse);
      });

      test('returns false for ConfigurationException', () {
        const exception = ConfigurationException('Config error');
        expect(ErrorHandler.isRecoverable(exception), isFalse);
      });

      test('returns false for unknown errors', () {
        expect(ErrorHandler.isRecoverable(Exception('unknown')), isFalse);
      });
    });
  });

  group('VaAulLTException subclasses', () {
    test('NetworkException toString includes type', () {
      const exception = NetworkException('Test error');
      expect(exception.toString(), contains('NetworkException'));
    });

    test('DockerException toString includes type', () {
      const exception = DockerException('Test error');
      expect(exception.toString(), contains('DockerException'));
    });

    test('SSHException toString includes type', () {
      const exception = SSHException('Test error');
      expect(exception.toString(), contains('SSHException'));
    });

    test('ConfigurationException toString includes type', () {
      const exception = ConfigurationException('Test error');
      expect(exception.toString(), contains('ConfigurationException'));
    });

    test('ServiceException toString includes type', () {
      const exception = ServiceException('Test error');
      expect(exception.toString(), contains('ServiceException'));
    });
  });

  group('ErrorHandlerMixin', () {
    test('provides convenient error handling methods', () {
      final testClass = _TestClassWithErrorHandler();
      
      final result = testClass.handleError(Exception('test error'));
      expect(result, isA<ServiceException>());
      expect(result.message, contains('_TestClassWithErrorHandler'));
    });

    test('getUserFriendlyMessage works through mixin', () {
      final testClass = _TestClassWithErrorHandler();
      const exception = NetworkException('Network error');
      
      final message = testClass.getUserFriendlyMessage(exception);
      expect(message, contains('Network connection issue'));
    });
  });
}

// Test class to verify ErrorHandlerMixin functionality
class _TestClassWithErrorHandler with ErrorHandlerMixin {
  // Test methods are provided by the mixin
}
