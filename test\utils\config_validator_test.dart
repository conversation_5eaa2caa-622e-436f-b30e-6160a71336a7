import 'package:flutter_test/flutter_test.dart';
import 'package:vaullt/utils/config_validator.dart';
import 'package:vaullt/models/vaultwarden_config.dart';
import 'dart:io';
import 'package:path/path.dart' as path;

void main() {
  group('ConfigValidator', () {
    group('validatePort', () {
      test('accepts valid port numbers', () {
        final result = ConfigValidator.validatePort(8080);
        expect(result.isValid, isTrue);
        expect(result.errors, isEmpty);
      });

      test('rejects port numbers below 1', () {
        final result = ConfigValidator.validatePort(0);
        expect(result.isValid, isFalse);
        expect(result.errors, contains('Port must be between 1 and 65535'));
      });

      test('rejects port numbers above 65535', () {
        final result = ConfigValidator.validatePort(70000);
        expect(result.isValid, isFalse);
        expect(result.errors, contains('Port must be between 1 and 65535'));
      });

      test('warns about privileged ports', () {
        final result = ConfigValidator.validatePort(80);
        expect(result.isValid, isTrue);
        expect(result.warnings, contains('Port 80 requires administrator privileges on most systems'));
      });

      test('warns about commonly used ports', () {
        final result = ConfigValidator.validatePort(3306);
        expect(result.isValid, isTrue);
        expect(result.warnings, contains('Port 3306 is commonly used by other services'));
      });
    });

    group('validateDataPath', () {
      test('accepts valid absolute paths', () {
        final tempDir = Directory.systemTemp.createTempSync('vaullt_test');
        try {
          final result = ConfigValidator.validateDataPath(tempDir.path);
          expect(result.isValid, isTrue);
        } finally {
          tempDir.deleteSync(recursive: true);
        }
      });

      test('rejects empty paths', () {
        final result = ConfigValidator.validateDataPath('');
        expect(result.isValid, isFalse);
        expect(result.errors, contains('Data path cannot be empty'));
      });

      test('warns about relative paths', () {
        final result = ConfigValidator.validateDataPath('relative/path');
        expect(result.warnings, contains('Relative paths may cause issues. Consider using absolute paths.'));
      });

      test('creates directory if it does not exist', () {
        final tempDir = Directory.systemTemp.createTempSync('vaullt_test');
        final testPath = path.join(tempDir.path, 'new_directory');
        
        try {
          final result = ConfigValidator.validateDataPath(testPath);
          expect(result.isValid, isTrue);
          expect(Directory(testPath).existsSync(), isTrue);
        } finally {
          tempDir.deleteSync(recursive: true);
        }
      });

      test('fails when directory cannot be created', () {
        // Use an invalid path that cannot be created
        final invalidPath = Platform.isWindows ? 'Z:\\invalid\\path' : '/root/invalid/path';
        final result = ConfigValidator.validateDataPath(invalidPath);
        expect(result.isValid, isFalse);
        expect(result.errors.any((error) => error.contains('Cannot create data directory')), isTrue);
      });
    });

    group('validateSecuritySettings', () {
      test('warns when signups are enabled', () {
        const config = VaultwardenConfig(
          signupsAllowed: true,
          dataPath: '/test/path',
          port: 8080,
          localhostOnly: true,
        );

        final result = ConfigValidator.validateSecuritySettings(config);
        expect(result.isValid, isTrue);
        expect(result.warnings, contains('Signups are enabled. Consider disabling after creating your account.'));
      });

      test('warns when network access is enabled', () {
        const config = VaultwardenConfig(
          signupsAllowed: false,
          dataPath: '/test/path',
          port: 8080,
          localhostOnly: false,
        );

        final result = ConfigValidator.validateSecuritySettings(config);
        expect(result.isValid, isTrue);
        expect(result.warnings, contains('Network access is enabled for LAN. Ensure your network is secure.'));
      });

      test('passes with secure settings', () {
        const config = VaultwardenConfig(
          signupsAllowed: false,
          dataPath: '/test/path',
          port: 8080,
          localhostOnly: true,
        );

        final result = ConfigValidator.validateSecuritySettings(config);
        expect(result.isValid, isTrue);
        expect(result.warnings, isEmpty);
      });
    });

    group('validateConfig', () {
      test('validates complete configuration successfully', () {
        final tempDir = Directory.systemTemp.createTempSync('vaullt_test');
        try {
          const config = VaultwardenConfig(
            signupsAllowed: false,
            dataPath: '/tmp/test',
            port: 8080,
            localhostOnly: true,
          );

          final result = ConfigValidator.validateConfig(config);
          expect(result.isValid, isTrue);
        } finally {
          tempDir.deleteSync(recursive: true);
        }
      });

      test('fails validation with invalid port and path', () {
        const config = VaultwardenConfig(
          signupsAllowed: false,
          dataPath: '',
          port: 70000,
          localhostOnly: true,
        );

        final result = ConfigValidator.validateConfig(config);
        expect(result.isValid, isFalse);
        expect(result.errors.length, greaterThan(1));
      });

      test('includes warnings in validation result', () {
        const config = VaultwardenConfig(
          signupsAllowed: true,
          dataPath: '/tmp/test',
          port: 80,
          localhostOnly: false,
        );

        final result = ConfigValidator.validateConfig(config);
        expect(result.warnings.length, greaterThan(0));
      });
    });

    group('getDefaultConfig', () {
      test('returns valid default configuration', () {
        final config = ConfigValidator.getDefaultConfig();
        
        expect(config.signupsAllowed, isFalse);
        expect(config.localhostOnly, isTrue);
        expect(config.port, equals(11001));
        expect(config.dataPath, isNotEmpty);
        
        final validation = ConfigValidator.validateConfig(config);
        expect(validation.isValid, isTrue);
      });

      test('default data path is platform appropriate', () {
        final config = ConfigValidator.getDefaultConfig();
        
        if (Platform.isWindows) {
          expect(config.dataPath, contains('VaAulLT'));
        } else if (Platform.isMacOS) {
          expect(config.dataPath, contains('Library/Application Support'));
        } else {
          expect(config.dataPath, contains('.local/share'));
        }
      });
    });

    group('sanitizeConfig', () {
      test('normalizes data path', () {
        const config = VaultwardenConfig(
          signupsAllowed: false,
          dataPath: '/test//path///with//extra//slashes',
          port: 8080,
          localhostOnly: true,
        );

        final sanitized = ConfigValidator.sanitizeConfig(config);
        expect(sanitized.dataPath, equals(path.normalize('/test/path/with/extra/slashes')));
      });

      test('clamps port to valid range', () {
        const config = VaultwardenConfig(
          signupsAllowed: false,
          dataPath: '/test/path',
          port: 70000,
          localhostOnly: true,
        );

        final sanitized = ConfigValidator.sanitizeConfig(config);
        expect(sanitized.port, equals(65535));
      });

      test('trims whitespace from data path', () {
        const config = VaultwardenConfig(
          signupsAllowed: false,
          dataPath: '  /test/path  ',
          port: 8080,
          localhostOnly: true,
        );

        final sanitized = ConfigValidator.sanitizeConfig(config);
        expect(sanitized.dataPath, equals(path.normalize('/test/path')));
      });
    });
  });

  group('ValidationResult', () {
    test('hasIssues returns true when there are errors', () {
      const result = ValidationResult(
        isValid: false,
        errors: ['Error 1'],
        warnings: [],
      );

      expect(result.hasIssues, isTrue);
    });

    test('hasIssues returns true when there are warnings', () {
      const result = ValidationResult(
        isValid: true,
        errors: [],
        warnings: ['Warning 1'],
      );

      expect(result.hasIssues, isTrue);
    });

    test('hasIssues returns false when no issues', () {
      const result = ValidationResult(
        isValid: true,
        errors: [],
        warnings: [],
      );

      expect(result.hasIssues, isFalse);
    });

    test('formattedMessage includes errors and warnings', () {
      const result = ValidationResult(
        isValid: false,
        errors: ['Error 1', 'Error 2'],
        warnings: ['Warning 1'],
      );

      final message = result.formattedMessage;
      expect(message, contains('Errors:'));
      expect(message, contains('Error 1'));
      expect(message, contains('Error 2'));
      expect(message, contains('Warnings:'));
      expect(message, contains('Warning 1'));
    });
  });
}
