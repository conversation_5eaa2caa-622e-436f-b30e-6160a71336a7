import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// Centralized logging service for the application
/// Provides structured logging with different levels and proper formatting
class LoggingService {
  static const String _appName = 'VaAulLT';
  
  /// Log levels for different types of messages
  enum LogLevel {
    debug,
    info,
    warning,
    error,
    critical,
  }

  /// Log a debug message (only in debug mode)
  static void debug(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    if (kDebugMode) {
      _log(LogLevel.debug, message, tag: tag, error: error, stackTrace: stackTrace);
    }
  }

  /// Log an informational message
  static void info(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.info, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log a warning message
  static void warning(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.warning, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log an error message
  static void error(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.error, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Log a critical error message
  static void critical(String message, {String? tag, Object? error, StackTrace? stackTrace}) {
    _log(LogLevel.critical, message, tag: tag, error: error, stackTrace: stackTrace);
  }

  /// Internal logging method
  static void _log(
    LogLevel level,
    String message, {
    String? tag,
    Object? error,
    StackTrace? stackTrace,
  }) {
    final timestamp = DateTime.now().toIso8601String();
    final levelStr = level.name.toUpperCase().padRight(8);
    final tagStr = tag != null ? '[$tag] ' : '';
    final formattedMessage = '$timestamp $levelStr $_appName: $tagStr$message';

    // Use appropriate logging method based on level
    switch (level) {
      case LogLevel.debug:
        developer.log(
          formattedMessage,
          name: _appName,
          level: 500, // Debug level
          error: error,
          stackTrace: stackTrace,
        );
        break;
      case LogLevel.info:
        developer.log(
          formattedMessage,
          name: _appName,
          level: 800, // Info level
          error: error,
          stackTrace: stackTrace,
        );
        break;
      case LogLevel.warning:
        developer.log(
          formattedMessage,
          name: _appName,
          level: 900, // Warning level
          error: error,
          stackTrace: stackTrace,
        );
        break;
      case LogLevel.error:
      case LogLevel.critical:
        developer.log(
          formattedMessage,
          name: _appName,
          level: 1000, // Error level
          error: error,
          stackTrace: stackTrace,
        );
        break;
    }

    // Also output to debug console for development
    if (kDebugMode) {
      debugPrint(formattedMessage);
      if (error != null) {
        debugPrint('Error: $error');
      }
      if (stackTrace != null) {
        debugPrint('Stack trace: $stackTrace');
      }
    }
  }

  /// Log network-related messages
  static void network(String message, {Object? error, StackTrace? stackTrace}) {
    info(message, tag: 'NETWORK', error: error, stackTrace: stackTrace);
  }

  /// Log Docker-related messages
  static void docker(String message, {Object? error, StackTrace? stackTrace}) {
    info(message, tag: 'DOCKER', error: error, stackTrace: stackTrace);
  }

  /// Log SSH-related messages
  static void ssh(String message, {Object? error, StackTrace? stackTrace}) {
    info(message, tag: 'SSH', error: error, stackTrace: stackTrace);
  }

  /// Log configuration-related messages
  static void config(String message, {Object? error, StackTrace? stackTrace}) {
    info(message, tag: 'CONFIG', error: error, stackTrace: stackTrace);
  }

  /// Log tunnel-related messages
  static void tunnel(String message, {Object? error, StackTrace? stackTrace}) {
    info(message, tag: 'TUNNEL', error: error, stackTrace: stackTrace);
  }

  /// Log service initialization messages
  static void service(String message, {Object? error, StackTrace? stackTrace}) {
    info(message, tag: 'SERVICE', error: error, stackTrace: stackTrace);
  }
}

/// Extension to add logging capabilities to exceptions
extension LoggableException on Exception {
  /// Log this exception with context
  void logError(String context, {String? tag}) {
    LoggingService.error(
      'Exception in $context: $this',
      tag: tag,
      error: this,
      stackTrace: StackTrace.current,
    );
  }
}

/// Mixin to add logging capabilities to classes
mixin LoggableMixin {
  /// Get the class name for logging
  String get logTag => runtimeType.toString();

  /// Log debug message with class tag
  void logDebug(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.debug(message, tag: logTag, error: error, stackTrace: stackTrace);
  }

  /// Log info message with class tag
  void logInfo(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.info(message, tag: logTag, error: error, stackTrace: stackTrace);
  }

  /// Log warning message with class tag
  void logWarning(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.warning(message, tag: logTag, error: error, stackTrace: stackTrace);
  }

  /// Log error message with class tag
  void logError(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.error(message, tag: logTag, error: error, stackTrace: stackTrace);
  }

  /// Log critical message with class tag
  void logCritical(String message, {Object? error, StackTrace? stackTrace}) {
    LoggingService.critical(message, tag: logTag, error: error, stackTrace: stackTrace);
  }
}
